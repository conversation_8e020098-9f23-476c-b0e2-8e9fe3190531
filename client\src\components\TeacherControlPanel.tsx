'use client';

import React, { useState } from 'react';
import { FiEdit3, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Lock, <PERSON>Unlock, FiChevronRight } from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';
import { useEditPermission } from '@/context/EditPermissionContext';
import { useSocketService } from '@/hooks/useSocketService';

interface TeacherControlPanelProps {
  className?: string;
}

export default function TeacherControlPanel({ className = '' }: TeacherControlPanelProps) {
  const { isTeacher, students, grantEditPermission, revokeEditPermission } = useEditPermission();
  const { isReady, isConnected } = useSocketService();
  const [pending, setPending] = useState<string | null>(null);
  const [isCollapsed, setIsCollapsed] = useState(false);

  // Only show panel for teachers
  if (!isTeacher) {
    return null;
  }

  const handleToggle = (student: any) => {
    setPending(student.socketId);
    if (student.canEdit) {
      revokeEditPermission(student.socketId);
    } else {
      grantEditPermission(student.socketId);
    }
    setTimeout(() => setPending(null), 1000);
  };

  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  return (
    <motion.div
      initial={{ x: 0 }}
      animate={{ x: isCollapsed ? 280 : 0 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className="fixed top-20 right-4 z-50"
    >
      {/* Collapse/Expand Toggle Button */}
      <motion.button
        onClick={toggleCollapse}
        className="absolute -left-10 top-4 bg-zinc-800 hover:bg-zinc-700 text-white p-2 rounded-l-lg shadow-lg transition-all duration-200 border border-zinc-600"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <motion.div
          animate={{ rotate: isCollapsed ? 180 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <FiChevronRight className="w-4 h-4" />
        </motion.div>
      </motion.button>

      {/* Main Panel */}
      <motion.aside
        className="w-80 bg-zinc-900 border border-zinc-700 rounded-2xl shadow-2xl overflow-hidden"
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.3 }}
      >
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <FiUsers className="text-white w-5 h-5" />
              <h2 className="text-lg font-bold text-white">Student Access</h2>
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-white/20 text-white text-xs px-2 py-1 rounded-full">
                {students.length} {students.length === 1 ? 'student' : 'students'}
              </span>
            </div>
          </div>
        </div>

        {/* Connection Status */}
        <AnimatePresence>
          {(!isReady || !isConnected) && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="bg-yellow-500/10 border-b border-yellow-500/20 px-4 py-2"
            >
              <div className="flex items-center space-x-2 text-yellow-400 text-sm">
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                <span>Connecting...</span>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Students List */}
        <div className="p-4 max-h-96 overflow-y-auto">
          <AnimatePresence>
            {students.length === 0 ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="text-zinc-400 flex flex-col items-center py-8 text-center"
              >
                <FiUsers className="w-12 h-12 mb-3 text-zinc-600" />
                <span className="text-sm">No students in the room yet</span>
                <span className="text-xs text-zinc-500 mt-1">Students will appear here when they join</span>
              </motion.div>
            ) : (
              <div className="space-y-3">
                {students.map((student, index) => (
                  <motion.div
                    key={student.socketId}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="group bg-zinc-800 hover:bg-zinc-750 rounded-xl p-3 transition-all duration-200 border border-zinc-700 hover:border-zinc-600"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                          {student.username.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <span className="font-medium text-white text-sm">{student.username}</span>
                          <div className="flex items-center space-x-1 mt-1">
                            {student.canEdit ? (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-md bg-green-500/20 text-green-400 text-xs">
                                <FiEdit3 className="w-3 h-3 mr-1" /> Can Edit
                              </span>
                            ) : (
                              <span className="inline-flex items-center px-2 py-0.5 rounded-md bg-zinc-600/50 text-zinc-300 text-xs">
                                <FiEye className="w-3 h-3 mr-1" /> View Only
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      <motion.button
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                        className={`flex items-center px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 ${
                          student.canEdit
                            ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30'
                            : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600 border border-zinc-600'
                        } ${
                          pending === student.socketId || !isReady || !isConnected
                            ? 'opacity-50 cursor-not-allowed'
                            : 'cursor-pointer'
                        }`}
                        onClick={() => handleToggle(student)}
                        disabled={pending === student.socketId || !isReady || !isConnected}
                        title={student.canEdit ? 'Revoke edit access' : 'Grant edit access'}
                      >
                        {pending === student.socketId ? (
                          <div className="w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-1"></div>
                        ) : student.canEdit ? (
                          <FiUnlock className="w-3 h-3 mr-1" />
                        ) : (
                          <FiLock className="w-3 h-3 mr-1" />
                        )}
                        {student.canEdit ? 'Revoke' : 'Grant'}
                      </motion.button>
                    </div>
                  </motion.div>
                ))}
              </div>
            )}
          </AnimatePresence>
        </div>
      </motion.aside>
    </motion.div>
  );
}
