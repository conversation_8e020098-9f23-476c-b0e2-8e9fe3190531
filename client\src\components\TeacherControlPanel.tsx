'use client';

import React, { useState, useEffect } from 'react';
import { FiEdit3, <PERSON><PERSON>ye, <PERSON><PERSON><PERSON>s, <PERSON>Lock, <PERSON>U<PERSON>lock, FiX } from 'react-icons/fi';
import { motion, AnimatePresence } from 'framer-motion';
import { useEditPermission } from '@/context/EditPermissionContext';
import { useSocketService } from '@/hooks/useSocketService';

export default function TeacherControlPanel() {
  const { isTeacher, students, grantEditPermission, revokeEditPermission } = useEditPermission();
  const { isReady, isConnected } = useSocketService();
  const [pending, setPending] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleToggle = (student: any) => {
    setPending(student.socketId);
    if (student.canEdit) {
      revokeEditPermission(student.socketId);
    } else {
      grantEditPermission(student.socketId);
    }
    setTimeout(() => setPending(null), 1000);
  };

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);

  // Close modal on Escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isModalOpen) {
        closeModal();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isModalOpen]);

  // Only show for teachers - moved after all hooks
  if (!isTeacher) {
    return null;
  }

  return (
    <div>
      {/* Floating Action Button */}
      <motion.button
        onClick={openModal}
        className="fixed top-4 right-4 z-40 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-3 rounded-full shadow-lg transition-all duration-200 border border-blue-500/30"
        whileHover={{ scale: 1.05, y: -2 }}
        whileTap={{ scale: 0.95 }}
        title="Manage Student Access"
      >
        <div className="relative">
          <FiUsers className="w-5 h-5" />
          {students.length > 0 && (
            <motion.span
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium"
            >
              {students.length}
            </motion.span>
          )}
        </div>
      </motion.button>

      {/* Modal Overlay & Content */}
      <AnimatePresence>
        {isModalOpen && (
          <div>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={closeModal}
              className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
            />

            {/* Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ type: "spring", duration: 0.5 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4"
            >
              <div className="bg-zinc-900 rounded-2xl shadow-2xl border border-zinc-700 w-full max-w-md max-h-[80vh] overflow-hidden">
                {/* Header */}
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <FiUsers className="text-white w-6 h-6" />
                      <div>
                        <h2 className="text-xl font-bold text-white">Student Access</h2>
                        <p className="text-blue-100 text-sm">Manage editing permissions</p>
                      </div>
                    </div>
                    <motion.button
                      onClick={closeModal}
                      className="text-white/80 hover:text-white p-1 rounded-lg hover:bg-white/10 transition-colors"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <FiX className="w-5 h-5" />
                    </motion.button>
                  </div>

                  {/* Stats */}
                  <div className="mt-4 flex items-center space-x-4">
                    <div className="bg-white/20 rounded-lg px-3 py-1">
                      <span className="text-white text-sm font-medium">
                        {students.length} {students.length === 1 ? 'Student' : 'Students'}
                      </span>
                    </div>
                    <div className="bg-white/20 rounded-lg px-3 py-1">
                      <span className="text-white text-sm font-medium">
                        {students.filter(s => s.canEdit).length} Can Edit
                      </span>
                    </div>
                  </div>
                </div>

                {/* Connection Status */}
                <AnimatePresence>
                  {(!isReady || !isConnected) && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="bg-yellow-500/10 border-b border-yellow-500/20 px-6 py-3"
                    >
                      <div className="flex items-center space-x-2 text-yellow-400 text-sm">
                        <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                        <span>Connecting to server...</span>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Students List */}
                <div className="p-6 max-h-96 overflow-y-auto">
                  <AnimatePresence>
                    {students.length === 0 ? (
                      <motion.div
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="text-zinc-400 flex flex-col items-center py-12 text-center"
                      >
                        <FiUsers className="w-16 h-16 mb-4 text-zinc-600" />
                        <span className="text-lg font-medium mb-2">No students yet</span>
                        <span className="text-sm text-zinc-500">Students will appear here when they join the room</span>
                      </motion.div>
                    ) : (
                      <div className="space-y-3">
                        {students.map((student, index) => (
                          <motion.div
                            key={student.socketId}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="group bg-zinc-800 hover:bg-zinc-750 rounded-xl p-4 transition-all duration-200 border border-zinc-700 hover:border-zinc-600"
                          >
                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                                  {student.username.charAt(0).toUpperCase()}
                                </div>
                                <div>
                                  <span className="font-medium text-white">{student.username}</span>
                                  <div className="flex items-center space-x-1 mt-1">
                                    {student.canEdit ? (
                                      <span className="inline-flex items-center px-2 py-1 rounded-md bg-green-500/20 text-green-400 text-xs font-medium">
                                        <FiEdit3 className="w-3 h-3 mr-1" /> Can Edit
                                      </span>
                                    ) : (
                                      <span className="inline-flex items-center px-2 py-1 rounded-md bg-zinc-600/50 text-zinc-300 text-xs font-medium">
                                        <FiEye className="w-3 h-3 mr-1" /> View Only
                                      </span>
                                    )}
                                  </div>
                                </div>
                              </div>

                              <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                                  student.canEdit
                                    ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30'
                                    : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600 border border-zinc-600'
                                } ${
                                  pending === student.socketId || !isReady || !isConnected
                                    ? 'opacity-50 cursor-not-allowed'
                                    : 'cursor-pointer'
                                }`}
                                onClick={() => handleToggle(student)}
                                disabled={pending === student.socketId || !isReady || !isConnected}
                                title={student.canEdit ? 'Revoke edit access' : 'Grant edit access'}
                              >
                                {pending === student.socketId ? (
                                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2"></div>
                                ) : student.canEdit ? (
                                  <FiUnlock className="w-4 h-4 mr-2" />
                                ) : (
                                  <FiLock className="w-4 h-4 mr-2" />
                                )}
                                {student.canEdit ? 'Revoke' : 'Grant'}
                              </motion.button>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            </motion.div>
          </div>
        )}
      </AnimatePresence>
    </div>
  );
}
