"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/app/editor/[roomId]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/editor/[roomId]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_CodeEditor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CodeEditor */ \"(app-pages-browser)/./src/components/CodeEditor.tsx\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _components_TeacherControlPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TeacherControlPanel */ \"(app-pages-browser)/./src/components/TeacherControlPanel.tsx\");\n/* harmony import */ var _components_PermissionBadge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/PermissionBadge */ \"(app-pages-browser)/./src/components/PermissionBadge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction EditorPage() {\n    _s();\n    // Get the params and handle the type safely\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    // Extract roomId and ensure it's a string\n    const roomId = params === null || params === void 0 ? void 0 : params.roomId;\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"Anonymous\") // Default username\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            // Debugging: Check if roomId is being extracted\n            console.log(\"Extracted roomId:\", roomId);\n            if (!roomId) {\n                console.error(\"Invalid or missing room ID\");\n                return;\n            }\n            if (true) {\n                const storedUsername = localStorage.getItem(\"username\");\n                if (storedUsername) {\n                    console.log(\"Using stored username:\", storedUsername);\n                    setUsername(storedUsername);\n                } else {\n                    console.log(\"No stored username found, redirecting to dashboard\");\n                    // Generate a random username suffix\n                    const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n                    const defaultUsername = \"User\".concat(randomSuffix);\n                    localStorage.setItem(\"username\", defaultUsername);\n                    setUsername(defaultUsername);\n                }\n            }\n        }\n    }[\"EditorPage.useEffect\"], [\n        roomId\n    ]);\n    if (!roomId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Error: Room ID is missing. Please join a valid room.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n            lineNumber: 44,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_5__.EditPermissionProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex flex-col items-center justify-center min-h-screen p-4 gap-6 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-blue-900/10 to-purple-900/10 dark:from-blue-900/20 dark:to-purple-900/20\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-grid-pattern opacity-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-7xl flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                children: [\n                                    \"RealCode - Room: \",\n                                    roomId\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PermissionBadge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: \"lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 58,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-7xl flex gap-6 h-[calc(100vh-200px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        roomId: roomId,\n                                        username: username\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PermissionBadge__WEBPACK_IMPORTED_MODULE_7__.EditorPermissionStatus, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeacherControlPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StudentListDebug, {}, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                lineNumber: 50,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n            lineNumber: 49,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorPage, \"z1zVpeEpIf1lxrjmMEpnU5z1etI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams\n    ];\n});\n_c = EditorPage;\nvar _c;\n$RefreshReg$(_c, \"EditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/editor/[roomId]/page.tsx\n"));

/***/ })

});