"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/app/editor/[roomId]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/editor/[roomId]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_CodeEditor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CodeEditor */ \"(app-pages-browser)/./src/components/CodeEditor.tsx\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _components_TeacherControlPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TeacherControlPanel */ \"(app-pages-browser)/./src/components/TeacherControlPanel.tsx\");\n/* harmony import */ var _components_PermissionBadge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/PermissionBadge */ \"(app-pages-browser)/./src/components/PermissionBadge.tsx\");\n/* harmony import */ var _components_StudentListDebug__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/StudentListDebug */ \"(app-pages-browser)/./src/components/StudentListDebug.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction EditorPage() {\n    _s();\n    // Get the params and handle the type safely\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    // Extract roomId and ensure it's a string\n    const roomId = params === null || params === void 0 ? void 0 : params.roomId;\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"Anonymous\") // Default username\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            // Debugging: Check if roomId is being extracted\n            console.log(\"Extracted roomId:\", roomId);\n            if (!roomId) {\n                console.error(\"Invalid or missing room ID\");\n                return;\n            }\n            if (true) {\n                const storedUsername = localStorage.getItem(\"username\");\n                if (storedUsername) {\n                    console.log(\"Using stored username:\", storedUsername);\n                    setUsername(storedUsername);\n                } else {\n                    console.log(\"No stored username found, redirecting to dashboard\");\n                    // Generate a random username suffix\n                    const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n                    const defaultUsername = \"User\".concat(randomSuffix);\n                    localStorage.setItem(\"username\", defaultUsername);\n                    setUsername(defaultUsername);\n                }\n            }\n        }\n    }[\"EditorPage.useEffect\"], [\n        roomId\n    ]);\n    if (!roomId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Error: Room ID is missing. Please join a valid room.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n            lineNumber: 45,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_5__.EditPermissionProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex flex-col items-center justify-center min-h-screen p-4 gap-6 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-blue-900/10 to-purple-900/10 dark:from-blue-900/20 dark:to-purple-900/20\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-grid-pattern opacity-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-7xl flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                children: [\n                                    \"RealCode - Room: \",\n                                    roomId\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PermissionBadge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: \"lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-7xl flex gap-6 h-[calc(100vh-200px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        roomId: roomId,\n                                        username: username\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PermissionBadge__WEBPACK_IMPORTED_MODULE_7__.EditorPermissionStatus, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeacherControlPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_StudentListDebug__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorPage, \"z1zVpeEpIf1lxrjmMEpnU5z1etI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams\n    ];\n});\n_c = EditorPage;\nvar _c;\n$RefreshReg$(_c, \"EditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/editor/[roomId]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/StudentListDebug.tsx":
/*!*********************************************!*\
  !*** ./src/components/StudentListDebug.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentListDebug)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction StudentListDebug() {\n    _s();\n    const { isTeacher, students, users } = (0,_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission)();\n    const { isReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService)();\n    const [eventLog, setEventLog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Log state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentListDebug.useEffect\": ()=>{\n            const timestamp = new Date().toLocaleTimeString();\n            const logEntry = \"[\".concat(timestamp, \"] Students: \").concat(students.length, \", Users: \").concat(users.length, \", Teacher: \").concat(isTeacher, \", Ready: \").concat(isReady, \", Connected: \").concat(isConnected);\n            setEventLog({\n                \"StudentListDebug.useEffect\": (prev)=>[\n                        logEntry,\n                        ...prev.slice(0, 9)\n                    ]\n            }[\"StudentListDebug.useEffect\"]); // Keep last 10 entries\n        }\n    }[\"StudentListDebug.useEffect\"], [\n        students.length,\n        users.length,\n        isTeacher,\n        isReady,\n        isConnected\n    ]);\n    if (!isTeacher) {\n        return null; // Only show for teachers\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-md z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"font-bold mb-2\",\n                children: \"\\uD83D\\uDD27 Debug Panel (Teacher)\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Status:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 9\n                    }, this),\n                    \" Ready: \",\n                    isReady ? '✅' : '❌',\n                    \", Connected: \",\n                    isConnected ? '✅' : '❌'\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                lineNumber: 27,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: [\n                            \"Students (\",\n                            students.length,\n                            \"):\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 9\n                    }, this),\n                    students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400\",\n                        children: \"No students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-xs\",\n                        children: students.map((student, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    student.username,\n                                    \" (\",\n                                    student.canEdit ? 'Edit' : 'View',\n                                    \")\"\n                                ]\n                            }, i, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                lineNumber: 31,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: [\n                            \"All Users (\",\n                            users.length,\n                            \"):\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400\",\n                        children: \"No users\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"text-xs\",\n                        children: users.map((user, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    user.username,\n                                    \" (\",\n                                    user.role,\n                                    \") - \",\n                                    user.canEdit ? 'Edit' : 'View'\n                                ]\n                            }, i, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Event Log:\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-300 max-h-20 overflow-y-auto\",\n                        children: eventLog.map((entry, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: entry\n                            }, i, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                                lineNumber: 65,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\StudentListDebug.tsx\",\n        lineNumber: 24,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentListDebug, \"4cvyw2rDgUevdRTlRyy4W/iAqBA=\", false, function() {\n    return [\n        _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission,\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService\n    ];\n});\n_c = StudentListDebug;\nvar _c;\n$RefreshReg$(_c, \"StudentListDebug\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1N0dWRlbnRMaXN0RGVidWcudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBRW1EO0FBQ2lCO0FBQ1I7QUFFN0MsU0FBU0s7O0lBQ3RCLE1BQU0sRUFBRUMsU0FBUyxFQUFFQyxRQUFRLEVBQUVDLEtBQUssRUFBRSxHQUFHTCxpRkFBaUJBO0lBQ3hELE1BQU0sRUFBRU0sT0FBTyxFQUFFQyxXQUFXLEVBQUUsR0FBR04seUVBQWdCQTtJQUNqRCxNQUFNLENBQUNPLFVBQVVDLFlBQVksR0FBR1YsK0NBQVFBLENBQVcsRUFBRTtJQUVyRCxvQkFBb0I7SUFDcEJELGdEQUFTQTtzQ0FBQztZQUNSLE1BQU1ZLFlBQVksSUFBSUMsT0FBT0Msa0JBQWtCO1lBQy9DLE1BQU1DLFdBQVcsSUFBNEJULE9BQXhCTSxXQUFVLGdCQUF5Q0wsT0FBM0JELFNBQVNVLE1BQU0sRUFBQyxhQUFxQ1gsT0FBMUJFLE1BQU1TLE1BQU0sRUFBQyxlQUFrQ1IsT0FBckJILFdBQVUsYUFBa0NJLE9BQXZCRCxTQUFRLGlCQUEyQixPQUFaQztZQUM5SUU7OENBQVlNLENBQUFBLE9BQVE7d0JBQUNGOzJCQUFhRSxLQUFLQyxLQUFLLENBQUMsR0FBRztxQkFBRzs4Q0FBRyx1QkFBdUI7UUFDL0U7cUNBQUc7UUFBQ1osU0FBU1UsTUFBTTtRQUFFVCxNQUFNUyxNQUFNO1FBQUVYO1FBQVdHO1FBQVNDO0tBQVk7SUFFbkUsSUFBSSxDQUFDSixXQUFXO1FBQ2QsT0FBTyxNQUFNLHlCQUF5QjtJQUN4QztJQUVBLHFCQUNFLDhEQUFDYztRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0M7Z0JBQUdELFdBQVU7MEJBQWlCOzs7Ozs7MEJBRS9CLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNFO2tDQUFPOzs7Ozs7b0JBQWdCO29CQUFTZCxVQUFVLE1BQU07b0JBQUk7b0JBQWNDLGNBQWMsTUFBTTs7Ozs7OzswQkFHekYsOERBQUNVO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0U7OzRCQUFPOzRCQUFXaEIsU0FBU1UsTUFBTTs0QkFBQzs7Ozs7OztvQkFDbENWLFNBQVNVLE1BQU0sS0FBSyxrQkFDbkIsOERBQUNHO3dCQUFJQyxXQUFVO2tDQUFnQjs7Ozs7NkNBRS9CLDhEQUFDRzt3QkFBR0gsV0FBVTtrQ0FDWGQsU0FBU2tCLEdBQUcsQ0FBQyxDQUFDQyxTQUFTQyxrQkFDdEIsOERBQUNDOztvQ0FDRUYsUUFBUUcsUUFBUTtvQ0FBQztvQ0FBR0gsUUFBUUksT0FBTyxHQUFHLFNBQVM7b0NBQU87OytCQURoREg7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUWpCLDhEQUFDUDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNFOzs0QkFBTzs0QkFBWWYsTUFBTVMsTUFBTTs0QkFBQzs7Ozs7OztvQkFDaENULE1BQU1TLE1BQU0sS0FBSyxrQkFDaEIsOERBQUNHO3dCQUFJQyxXQUFVO2tDQUFnQjs7Ozs7NkNBRS9CLDhEQUFDRzt3QkFBR0gsV0FBVTtrQ0FDWGIsTUFBTWlCLEdBQUcsQ0FBQyxDQUFDTSxNQUFNSixrQkFDaEIsOERBQUNDOztvQ0FDRUcsS0FBS0YsUUFBUTtvQ0FBQztvQ0FBR0UsS0FBS0MsSUFBSTtvQ0FBQztvQ0FBS0QsS0FBS0QsT0FBTyxHQUFHLFNBQVM7OytCQURsREg7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUWpCLDhEQUFDUDs7a0NBQ0MsOERBQUNHO2tDQUFPOzs7Ozs7a0NBQ1IsOERBQUNIO3dCQUFJQyxXQUFVO2tDQUNaVixTQUFTYyxHQUFHLENBQUMsQ0FBQ1EsT0FBT04sa0JBQ3BCLDhEQUFDUDswQ0FBYWE7K0JBQUpOOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTXRCO0dBaEV3QnRCOztRQUNpQkYsNkVBQWlCQTtRQUN2QkMscUVBQWdCQTs7O0tBRjNCQyIsInNvdXJjZXMiOlsiQzpcXFByb2plY3RcXHJlYWxjb2RlXFxjbGllbnRcXHNyY1xcY29tcG9uZW50c1xcU3R1ZGVudExpc3REZWJ1Zy50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZUVkaXRQZXJtaXNzaW9uIH0gZnJvbSAnQC9jb250ZXh0L0VkaXRQZXJtaXNzaW9uQ29udGV4dCc7XG5pbXBvcnQgeyB1c2VTb2NrZXRTZXJ2aWNlIH0gZnJvbSAnQC9ob29rcy91c2VTb2NrZXRTZXJ2aWNlJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3R1ZGVudExpc3REZWJ1ZygpIHtcbiAgY29uc3QgeyBpc1RlYWNoZXIsIHN0dWRlbnRzLCB1c2VycyB9ID0gdXNlRWRpdFBlcm1pc3Npb24oKTtcbiAgY29uc3QgeyBpc1JlYWR5LCBpc0Nvbm5lY3RlZCB9ID0gdXNlU29ja2V0U2VydmljZSgpO1xuICBjb25zdCBbZXZlbnRMb2csIHNldEV2ZW50TG9nXSA9IHVzZVN0YXRlPHN0cmluZ1tdPihbXSk7XG5cbiAgLy8gTG9nIHN0YXRlIGNoYW5nZXNcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB0aW1lc3RhbXAgPSBuZXcgRGF0ZSgpLnRvTG9jYWxlVGltZVN0cmluZygpO1xuICAgIGNvbnN0IGxvZ0VudHJ5ID0gYFske3RpbWVzdGFtcH1dIFN0dWRlbnRzOiAke3N0dWRlbnRzLmxlbmd0aH0sIFVzZXJzOiAke3VzZXJzLmxlbmd0aH0sIFRlYWNoZXI6ICR7aXNUZWFjaGVyfSwgUmVhZHk6ICR7aXNSZWFkeX0sIENvbm5lY3RlZDogJHtpc0Nvbm5lY3RlZH1gO1xuICAgIHNldEV2ZW50TG9nKHByZXYgPT4gW2xvZ0VudHJ5LCAuLi5wcmV2LnNsaWNlKDAsIDkpXSk7IC8vIEtlZXAgbGFzdCAxMCBlbnRyaWVzXG4gIH0sIFtzdHVkZW50cy5sZW5ndGgsIHVzZXJzLmxlbmd0aCwgaXNUZWFjaGVyLCBpc1JlYWR5LCBpc0Nvbm5lY3RlZF0pO1xuXG4gIGlmICghaXNUZWFjaGVyKSB7XG4gICAgcmV0dXJuIG51bGw7IC8vIE9ubHkgc2hvdyBmb3IgdGVhY2hlcnNcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmaXhlZCBib3R0b20tNCByaWdodC00IGJnLWJsYWNrIHRleHQtd2hpdGUgcC00IHJvdW5kZWQtbGcgdGV4dC14cyBtYXgtdy1tZCB6LTUwXCI+XG4gICAgICA8aDMgY2xhc3NOYW1lPVwiZm9udC1ib2xkIG1iLTJcIj7wn5SnIERlYnVnIFBhbmVsIChUZWFjaGVyKTwvaDM+XG4gICAgICBcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMlwiPlxuICAgICAgICA8c3Ryb25nPlN0YXR1czo8L3N0cm9uZz4gUmVhZHk6IHtpc1JlYWR5ID8gJ+KchScgOiAn4p2MJ30sIENvbm5lY3RlZDoge2lzQ29ubmVjdGVkID8gJ+KchScgOiAn4p2MJ31cbiAgICAgIDwvZGl2PlxuICAgICAgXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTJcIj5cbiAgICAgICAgPHN0cm9uZz5TdHVkZW50cyAoe3N0dWRlbnRzLmxlbmd0aH0pOjwvc3Ryb25nPlxuICAgICAgICB7c3R1ZGVudHMubGVuZ3RoID09PSAwID8gKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMFwiPk5vIHN0dWRlbnRzPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPHVsIGNsYXNzTmFtZT1cInRleHQteHNcIj5cbiAgICAgICAgICAgIHtzdHVkZW50cy5tYXAoKHN0dWRlbnQsIGkpID0+IChcbiAgICAgICAgICAgICAgPGxpIGtleT17aX0+XG4gICAgICAgICAgICAgICAge3N0dWRlbnQudXNlcm5hbWV9ICh7c3R1ZGVudC5jYW5FZGl0ID8gJ0VkaXQnIDogJ1ZpZXcnfSlcbiAgICAgICAgICAgICAgPC9saT5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvdWw+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cblxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYi0yXCI+XG4gICAgICAgIDxzdHJvbmc+QWxsIFVzZXJzICh7dXNlcnMubGVuZ3RofSk6PC9zdHJvbmc+XG4gICAgICAgIHt1c2Vycy5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNDAwXCI+Tm8gdXNlcnM8L2Rpdj5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwidGV4dC14c1wiPlxuICAgICAgICAgICAge3VzZXJzLm1hcCgodXNlciwgaSkgPT4gKFxuICAgICAgICAgICAgICA8bGkga2V5PXtpfT5cbiAgICAgICAgICAgICAgICB7dXNlci51c2VybmFtZX0gKHt1c2VyLnJvbGV9KSAtIHt1c2VyLmNhbkVkaXQgPyAnRWRpdCcgOiAnVmlldyd9XG4gICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICApKX1cbiAgICAgICAgICA8L3VsPlxuICAgICAgICApfVxuICAgICAgPC9kaXY+XG5cbiAgICAgIDxkaXY+XG4gICAgICAgIDxzdHJvbmc+RXZlbnQgTG9nOjwvc3Ryb25nPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTMwMCBtYXgtaC0yMCBvdmVyZmxvdy15LWF1dG9cIj5cbiAgICAgICAgICB7ZXZlbnRMb2cubWFwKChlbnRyeSwgaSkgPT4gKFxuICAgICAgICAgICAgPGRpdiBrZXk9e2l9PntlbnRyeX08L2Rpdj5cbiAgICAgICAgICApKX1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwidXNlRWRpdFBlcm1pc3Npb24iLCJ1c2VTb2NrZXRTZXJ2aWNlIiwiU3R1ZGVudExpc3REZWJ1ZyIsImlzVGVhY2hlciIsInN0dWRlbnRzIiwidXNlcnMiLCJpc1JlYWR5IiwiaXNDb25uZWN0ZWQiLCJldmVudExvZyIsInNldEV2ZW50TG9nIiwidGltZXN0YW1wIiwiRGF0ZSIsInRvTG9jYWxlVGltZVN0cmluZyIsImxvZ0VudHJ5IiwibGVuZ3RoIiwicHJldiIsInNsaWNlIiwiZGl2IiwiY2xhc3NOYW1lIiwiaDMiLCJzdHJvbmciLCJ1bCIsIm1hcCIsInN0dWRlbnQiLCJpIiwibGkiLCJ1c2VybmFtZSIsImNhbkVkaXQiLCJ1c2VyIiwicm9sZSIsImVudHJ5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/StudentListDebug.tsx\n"));

/***/ })

});