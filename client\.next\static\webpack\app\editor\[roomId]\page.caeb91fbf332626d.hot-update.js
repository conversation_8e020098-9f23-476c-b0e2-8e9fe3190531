"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/TeacherControlPanel.tsx":
/*!************************************************!*\
  !*** ./src/components/TeacherControlPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherControlPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit3,FiEye,FiLock,FiUnlock,FiUsers,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TeacherControlPanel() {\n    _s();\n    const { isTeacher, students, grantEditPermission, revokeEditPermission } = (0,_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission)();\n    const { isReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService)();\n    const [pending, setPending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleToggle = (student)=>{\n        setPending(student.socketId);\n        if (student.canEdit) {\n            revokeEditPermission(student.socketId);\n        } else {\n            grantEditPermission(student.socketId);\n        }\n        setTimeout(()=>setPending(null), 1000);\n    };\n    const openModal = ()=>setIsModalOpen(true);\n    const closeModal = ()=>setIsModalOpen(false);\n    // Close modal on Escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherControlPanel.useEffect\": ()=>{\n            const handleEscape = {\n                \"TeacherControlPanel.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isModalOpen) {\n                        closeModal();\n                    }\n                }\n            }[\"TeacherControlPanel.useEffect.handleEscape\"];\n            document.addEventListener('keydown', handleEscape);\n            return ({\n                \"TeacherControlPanel.useEffect\": ()=>document.removeEventListener('keydown', handleEscape)\n            })[\"TeacherControlPanel.useEffect\"];\n        }\n    }[\"TeacherControlPanel.useEffect\"], [\n        isModalOpen\n    ]);\n    // Only show for teachers - moved after all hooks\n    if (!isTeacher) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                onClick: openModal,\n                className: \"fixed top-4 right-4 z-40 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-3 rounded-full shadow-lg transition-all duration-200 border border-blue-500/30\",\n                whileHover: {\n                    scale: 1.05,\n                    y: -2\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                title: \"Manage Student Access\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 56,\n                            columnNumber: 11\n                        }, this),\n                        students.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                            initial: {\n                                scale: 0\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium\",\n                            children: students.length\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            onClick: closeModal,\n                            className: \"fixed inset-0 bg-black/60 backdrop-blur-sm z-50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                scale: 0.9,\n                                y: 20\n                            },\n                            transition: {\n                                type: \"spring\",\n                                duration: 0.5\n                            },\n                            className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-zinc-900 rounded-2xl shadow-2xl border border-zinc-700 w-full max-w-md max-h-[80vh] overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {\n                                                                className: \"text-white w-6 h-6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                lineNumber: 95,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-xl font-bold text-white\",\n                                                                        children: \"Student Access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                        lineNumber: 97,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-100 text-sm\",\n                                                                        children: \"Manage editing permissions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                        lineNumber: 98,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                lineNumber: 96,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 94,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                        onClick: closeModal,\n                                                        className: \"text-white/80 hover:text-white p-1 rounded-lg hover:bg-white/10 transition-colors\",\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiX, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 rounded-lg px-3 py-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-medium\",\n                                                            children: [\n                                                                students.length,\n                                                                \" \",\n                                                                students.length === 1 ? 'Student' : 'Students'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 113,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 rounded-lg px-3 py-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-medium\",\n                                                            children: [\n                                                                students.filter((s)=>s.canEdit).length,\n                                                                \" Can Edit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 119,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 118,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 112,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                                        children: (!isReady || !isConnected) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: 'auto'\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"bg-yellow-500/10 border-b border-yellow-500/20 px-6 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-yellow-400 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Connecting to server...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 max-h-96 overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                className: \"text-zinc-400 flex flex-col items-center py-12 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {\n                                                        className: \"w-16 h-16 mb-4 text-zinc-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-medium mb-2\",\n                                                        children: \"No students yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-zinc-500\",\n                                                        children: \"Students will appear here when they join the room\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: students.map((student, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            delay: index * 0.1\n                                                        },\n                                                        className: \"group bg-zinc-800 hover:bg-zinc-750 rounded-xl p-4 transition-all duration-200 border border-zinc-700 hover:border-zinc-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium\",\n                                                                            children: student.username.charAt(0).toUpperCase()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 168,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-white\",\n                                                                                    children: student.username\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                    lineNumber: 172,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-1 mt-1\",\n                                                                                    children: student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-flex items-center px-2 py-1 rounded-md bg-green-500/20 text-green-400 text-xs font-medium\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEdit3, {\n                                                                                                className: \"w-3 h-3 mr-1\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                                lineNumber: 176,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            \" Can Edit\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                        lineNumber: 175,\n                                                                                        columnNumber: 39\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-flex items-center px-2 py-1 rounded-md bg-zinc-600/50 text-zinc-300 text-xs font-medium\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEye, {\n                                                                                                className: \"w-3 h-3 mr-1\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                                lineNumber: 180,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            \" View Only\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                        lineNumber: 179,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                    lineNumber: 173,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 171,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    className: \"flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(student.canEdit ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600 border border-zinc-600', \" \").concat(pending === student.socketId || !isReady || !isConnected ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                                                    onClick: ()=>handleToggle(student),\n                                                                    disabled: pending === student.socketId || !isReady || !isConnected,\n                                                                    title: student.canEdit ? 'Revoke edit access' : 'Grant edit access',\n                                                                    children: [\n                                                                        pending === student.socketId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 204,\n                                                                            columnNumber: 35\n                                                                        }, this) : student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUnlock, {\n                                                                            className: \"w-4 h-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 206,\n                                                                            columnNumber: 35\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLock, {\n                                                                            className: \"w-4 h-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 208,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        student.canEdit ? 'Revoke' : 'Grant'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, student.socketId, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 70,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherControlPanel, \"XXvsg8u3UHfjO4bxcw/s4839Jw0=\", false, function() {\n    return [\n        _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission,\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService\n    ];\n});\n_c = TeacherControlPanel;\nvar _c;\n$RefreshReg$(_c, \"TeacherControlPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TeacherControlPanel.tsx\n"));

/***/ })

});