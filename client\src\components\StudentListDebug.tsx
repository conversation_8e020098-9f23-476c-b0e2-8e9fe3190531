'use client';

import React, { useEffect, useState } from 'react';
import { useEditPermission } from '@/context/EditPermissionContext';
import { useSocketService } from '@/hooks/useSocketService';

export default function StudentListDebug() {
  const { isTeacher, students, users } = useEditPermission();
  const { isReady, isConnected } = useSocketService();
  const [eventLog, setEventLog] = useState<string[]>([]);

  // Log state changes
  useEffect(() => {
    const timestamp = new Date().toLocaleTimeString();
    const logEntry = `[${timestamp}] Students: ${students.length}, Users: ${users.length}, Teacher: ${isTeacher}, Ready: ${isReady}, Connected: ${isConnected}`;
    setEventLog(prev => [logEntry, ...prev.slice(0, 9)]); // Keep last 10 entries
  }, [students.length, users.length, isTeacher, isReady, isConnected]);

  if (!isTeacher) {
    return null; // Only show for teachers
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black text-white p-4 rounded-lg text-xs max-w-md z-50">
      <h3 className="font-bold mb-2">🔧 Debug Panel (Teacher)</h3>
      
      <div className="mb-2">
        <strong>Status:</strong> Ready: {isReady ? '✅' : '❌'}, Connected: {isConnected ? '✅' : '❌'}
      </div>
      
      <div className="mb-2">
        <strong>Students ({students.length}):</strong>
        {students.length === 0 ? (
          <div className="text-gray-400">No students</div>
        ) : (
          <ul className="text-xs">
            {students.map((student, i) => (
              <li key={i}>
                {student.username} ({student.canEdit ? 'Edit' : 'View'})
              </li>
            ))}
          </ul>
        )}
      </div>

      <div className="mb-2">
        <strong>All Users ({users.length}):</strong>
        {users.length === 0 ? (
          <div className="text-gray-400">No users</div>
        ) : (
          <ul className="text-xs">
            {users.map((user, i) => (
              <li key={i}>
                {user.username} ({user.role}) - {user.canEdit ? 'Edit' : 'View'}
              </li>
            ))}
          </ul>
        )}
      </div>

      <div>
        <strong>Event Log:</strong>
        <div className="text-xs text-gray-300 max-h-20 overflow-y-auto">
          {eventLog.map((entry, i) => (
            <div key={i}>{entry}</div>
          ))}
        </div>
      </div>
    </div>
  );
}
