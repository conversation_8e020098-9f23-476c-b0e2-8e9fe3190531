"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/context/EditPermissionContext.tsx":
/*!***********************************************!*\
  !*** ./src/context/EditPermissionContext.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditPermissionProvider: () => (/* binding */ EditPermissionProvider),\n/* harmony export */   useEditPermission: () => (/* binding */ useEditPermission)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ EditPermissionProvider,useEditPermission auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst EditPermissionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction EditPermissionProvider(param) {\n    let { children } = param;\n    _s();\n    const [canEdit, setCanEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTeacher, setIsTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Use the socket service hook\n    const { socketService, isReady: socketReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService)();\n    // Compute permission badge based on current state\n    const permissionBadge = isTeacher ? 'teacher' : canEdit ? 'edit-access' : 'view-only';\n    // Grant edit permission to a student\n    const grantEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can grant edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot grant edit permission');\n            return;\n        }\n        // Get room ID from URL path\n        const roomId =  true ? window.location.pathname.split('/').pop() : 0;\n        if (!roomId) {\n            console.error('No room ID found in URL path');\n            return;\n        }\n        console.log(\"Granting edit permission to \".concat(targetSocketId, \" in room \").concat(roomId));\n        try {\n            // Use the socket directly for custom events\n            const sock = socketService.getSocket && socketService.getSocket();\n            if (sock && typeof sock.emit === 'function') {\n                sock.emit('grant-edit-permission', {\n                    roomId,\n                    targetSocketId\n                });\n            } else {\n                console.error('Socket instance not available for grant-edit-permission');\n            }\n        } catch (error) {\n            console.error('Error granting edit permission:', error);\n        }\n    };\n    // Revoke edit permission from a student\n    const revokeEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can revoke edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot revoke edit permission');\n            return;\n        }\n        // Get room ID from URL path\n        const roomId =  true ? window.location.pathname.split('/').pop() : 0;\n        if (!roomId) {\n            console.error('No room ID found in URL path');\n            return;\n        }\n        console.log(\"Revoking edit permission from \".concat(targetSocketId, \" in room \").concat(roomId));\n        try {\n            // Use the socket directly for custom events\n            const sock = socketService.getSocket && socketService.getSocket();\n            if (sock && typeof sock.emit === 'function') {\n                sock.emit('revoke-edit-permission', {\n                    roomId,\n                    targetSocketId\n                });\n            } else {\n                console.error('Socket instance not available for revoke-edit-permission');\n            }\n        } catch (error) {\n            console.error('Error revoking edit permission:', error);\n        }\n    };\n    // Legacy method for backward compatibility\n    const setEditPermission = (targetSocketId, canEdit)=>{\n        if (canEdit) {\n            grantEditPermission(targetSocketId);\n        } else {\n            revokeEditPermission(targetSocketId);\n        }\n    };\n    const updateUserPermission = (socketId, canEdit)=>{\n        setUsers((prevUsers)=>prevUsers.map((user)=>user.socketId === socketId ? {\n                    ...user,\n                    canEdit\n                } : user));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            // Only set up listeners when socket is ready\n            if (!socketReady || !socketService) {\n                console.log('Waiting for socket to be ready for edit permissions...');\n                return;\n            }\n            // Listen for edit permission changes\n            const handleEditPermission = {\n                \"EditPermissionProvider.useEffect.handleEditPermission\": (data)=>{\n                    console.log('Received edit permission update:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handleEditPermission\"];\n            // Listen for permission updates (new event)\n            const handlePermissionUpdated = {\n                \"EditPermissionProvider.useEffect.handlePermissionUpdated\": (data)=>{\n                    console.log('Received permission-updated event:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handlePermissionUpdated\"];\n            // Listen for student list updates (for teachers) - ROBUST VERSION\n            const handleUpdateStudentList = {\n                \"EditPermissionProvider.useEffect.handleUpdateStudentList\": (data)=>{\n                    console.log('🎯 [ROBUST] Received update-student-list event:', JSON.stringify(data, null, 2));\n                    // Robust validation\n                    if (!data) {\n                        console.warn('🚨 [ROBUST] update-student-list event received with null/undefined data');\n                        return;\n                    }\n                    if (!data.students) {\n                        console.warn('🚨 [ROBUST] update-student-list event missing students property:', data);\n                        return;\n                    }\n                    if (!Array.isArray(data.students)) {\n                        console.warn('🚨 [ROBUST] update-student-list students is not an array:', typeof data.students, data.students);\n                        return;\n                    }\n                    console.log(\"✅ [ROBUST] Valid students array received with \".concat(data.students.length, \" items\"));\n                    // The backend already filters to only students, so we trust the data\n                    // Just ensure each student has required properties\n                    const validStudents = data.students.filter({\n                        \"EditPermissionProvider.useEffect.handleUpdateStudentList.validStudents\": (student)=>{\n                            const isValid = student && typeof student === 'object' && student.socketId && student.username && student.userId;\n                            if (!isValid) {\n                                console.warn('🚨 [ROBUST] Invalid student object:', student);\n                            }\n                            return isValid;\n                        }\n                    }[\"EditPermissionProvider.useEffect.handleUpdateStudentList.validStudents\"]);\n                    console.log(\"✅ [ROBUST] Setting \".concat(validStudents.length, \" valid students to state\"));\n                    setStudents(validStudents);\n                }\n            }[\"EditPermissionProvider.useEffect.handleUpdateStudentList\"];\n            // 🔥 FIXED: Unified handler for room users updates\n            const handleRoomUsersUpdated = {\n                \"EditPermissionProvider.useEffect.handleRoomUsersUpdated\": (data)=>{\n                    console.log('🔄 [TEACHER_SYNC] Room users updated with permissions:', data);\n                    if (!data || !Array.isArray(data.users)) {\n                        console.warn('⚠️ [TEACHER_SYNC] Invalid room users data received:', data);\n                        return;\n                    }\n                    // Update the users state\n                    setUsers(data.users);\n                    // 🔥 KEY FIX: Also update students state for teachers in real-time\n                    if (isTeacher) {\n                        const studentsFromUsers = data.users.filter({\n                            \"EditPermissionProvider.useEffect.handleRoomUsersUpdated.studentsFromUsers\": (user)=>user.role === 'student'\n                        }[\"EditPermissionProvider.useEffect.handleRoomUsersUpdated.studentsFromUsers\"]).map({\n                            \"EditPermissionProvider.useEffect.handleRoomUsersUpdated.studentsFromUsers\": (user)=>({\n                                    socketId: user.socketId,\n                                    username: user.username,\n                                    userId: user.userId,\n                                    canEdit: user.canEdit || false,\n                                    joinedAt: new Date().toISOString(),\n                                    lastActivity: new Date().toISOString()\n                                })\n                        }[\"EditPermissionProvider.useEffect.handleRoomUsersUpdated.studentsFromUsers\"]);\n                        console.log(\"\\uD83D\\uDD04 [TEACHER_SYNC] Updating students from room-users-updated: \".concat(studentsFromUsers.length, \" students\"));\n                        setStudents(studentsFromUsers);\n                    }\n                }\n            }[\"EditPermissionProvider.useEffect.handleRoomUsersUpdated\"];\n            // 🔥 OPTIMIZED: Simplified user-joined handler (room-users-updated handles the state)\n            const handleUserJoined = {\n                \"EditPermissionProvider.useEffect.handleUserJoined\": (data)=>{\n                    console.log('🔄 [TEACHER_SYNC] User joined event received:', data);\n                // The room-users-updated event will handle the actual state update automatically\n                // This is just for logging and potential future enhancements\n                }\n            }[\"EditPermissionProvider.useEffect.handleUserJoined\"];\n            // Set up socket listeners with null checks\n            try {\n                socketService.on('edit-permission', handleEditPermission);\n                socketService.on('permission-updated', handlePermissionUpdated);\n                socketService.on('update-student-list', handleUpdateStudentList);\n                socketService.on('room-users-updated', handleRoomUsersUpdated);\n                socketService.on('user-joined', handleUserJoined);\n                console.log('Enhanced permission socket listeners set up successfully');\n            } catch (error) {\n                console.error('Error setting up socket listeners:', error);\n            }\n            // Cleanup listeners\n            return ({\n                \"EditPermissionProvider.useEffect\": ()=>{\n                    try {\n                        if (socketService) {\n                            socketService.off('edit-permission', handleEditPermission);\n                            socketService.off('permission-updated', handlePermissionUpdated);\n                            socketService.off('update-student-list', handleUpdateStudentList);\n                            socketService.off('room-users-updated', handleRoomUsersUpdated);\n                            socketService.off('user-joined', handleUserJoined);\n                            console.log('Enhanced permission socket listeners cleaned up');\n                        }\n                    } catch (error) {\n                        console.error('Error cleaning up socket listeners:', error);\n                    }\n                }\n            })[\"EditPermissionProvider.useEffect\"];\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        socketReady\n    ]);\n    // In the effect that requests the student list, use the correct public method\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            if (!socketReady || !socketService || !isTeacher) {\n                return;\n            }\n            // If this is a teacher, request the initial student list\n            const currentRoomId = window.location.pathname.split('/').pop();\n            if (currentRoomId) {\n                console.log('Teacher role detected, requesting initial student list for room:', currentRoomId);\n                // FIX: Use the underlying socket directly for custom events\n                const sock = socketService.getSocket && socketService.getSocket();\n                if (sock && typeof sock.emit === 'function') {\n                    sock.emit('request-student-list', {\n                        roomId: currentRoomId\n                    });\n                } else {\n                    console.warn('Socket instance not available for request-student-list emit');\n                }\n            }\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        socketReady,\n        isTeacher,\n        socketService\n    ]);\n    // Log permission changes for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            console.log(\"Edit permission state: canEdit=\".concat(canEdit, \", isTeacher=\").concat(isTeacher, \", socketReady=\").concat(socketReady, \", isConnected=\").concat(isConnected));\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        canEdit,\n        isTeacher,\n        socketReady,\n        isConnected\n    ]);\n    const value = {\n        canEdit,\n        isTeacher,\n        users,\n        students,\n        permissionBadge,\n        grantEditPermission,\n        revokeEditPermission,\n        setEditPermission,\n        updateUserPermission,\n        setUsers,\n        setStudents,\n        setCanEdit,\n        setIsTeacher\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditPermissionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\context\\\\EditPermissionContext.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n_s(EditPermissionProvider, \"udh6tS1itqpINKcvqOf60EZ6C1A=\", false, function() {\n    return [\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService\n    ];\n});\n_c = EditPermissionProvider;\nfunction useEditPermission() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(EditPermissionContext);\n    if (context === undefined) {\n        throw new Error('useEditPermission must be used within an EditPermissionProvider');\n    }\n    return context;\n}\n_s1(useEditPermission, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"EditPermissionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/EditPermissionContext.tsx\n"));

/***/ })

});