"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/TeacherControlPanel.tsx":
/*!************************************************!*\
  !*** ./src/components/TeacherControlPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherControlPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit3,FiEye,FiLock,FiUnlock,FiUsers,FiX!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TeacherControlPanel() {\n    let {} = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    _s();\n    const { isTeacher, students, grantEditPermission, revokeEditPermission } = (0,_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission)();\n    const { isReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService)();\n    const [pending, setPending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isModalOpen, setIsModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleToggle = (student)=>{\n        setPending(student.socketId);\n        if (student.canEdit) {\n            revokeEditPermission(student.socketId);\n        } else {\n            grantEditPermission(student.socketId);\n        }\n        setTimeout(()=>setPending(null), 1000);\n    };\n    const openModal = ()=>setIsModalOpen(true);\n    const closeModal = ()=>setIsModalOpen(false);\n    // Close modal on Escape key\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherControlPanel.useEffect\": ()=>{\n            const handleEscape = {\n                \"TeacherControlPanel.useEffect.handleEscape\": (e)=>{\n                    if (e.key === 'Escape' && isModalOpen) {\n                        closeModal();\n                    }\n                }\n            }[\"TeacherControlPanel.useEffect.handleEscape\"];\n            document.addEventListener('keydown', handleEscape);\n            return ({\n                \"TeacherControlPanel.useEffect\": ()=>document.removeEventListener('keydown', handleEscape)\n            })[\"TeacherControlPanel.useEffect\"];\n        }\n    }[\"TeacherControlPanel.useEffect\"], [\n        isModalOpen\n    ]);\n    // Only show for teachers - moved after all hooks\n    if (!isTeacher) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                onClick: openModal,\n                className: \"fixed top-4 right-4 z-40 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white p-3 rounded-full shadow-lg transition-all duration-200 border border-blue-500/30\",\n                whileHover: {\n                    scale: 1.05,\n                    y: -2\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                title: \"Manage Student Access\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {\n                            className: \"w-5 h-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        students.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.span, {\n                            initial: {\n                                scale: 0\n                            },\n                            animate: {\n                                scale: 1\n                            },\n                            className: \"absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium\",\n                            children: students.length\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: isModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0\n                            },\n                            animate: {\n                                opacity: 1\n                            },\n                            exit: {\n                                opacity: 0\n                            },\n                            onClick: closeModal,\n                            className: \"fixed inset-0 bg-black/60 backdrop-blur-sm z-50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                scale: 0.9,\n                                y: 20\n                            },\n                            animate: {\n                                opacity: 1,\n                                scale: 1,\n                                y: 0\n                            },\n                            exit: {\n                                opacity: 0,\n                                scale: 0.9,\n                                y: 20\n                            },\n                            transition: {\n                                type: \"spring\",\n                                duration: 0.5\n                            },\n                            className: \"fixed inset-0 z-50 flex items-center justify-center p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-zinc-900 rounded-2xl shadow-2xl border border-zinc-700 w-full max-w-md max-h-[80vh] overflow-hidden\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {\n                                                                className: \"text-white w-6 h-6\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                lineNumber: 97,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                        className: \"text-xl font-bold text-white\",\n                                                                        children: \"Student Access\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                        lineNumber: 99,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-blue-100 text-sm\",\n                                                                        children: \"Manage editing permissions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                        lineNumber: 100,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                lineNumber: 98,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 96,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                        onClick: closeModal,\n                                                        className: \"text-white/80 hover:text-white p-1 rounded-lg hover:bg-white/10 transition-colors\",\n                                                        whileHover: {\n                                                            scale: 1.1\n                                                        },\n                                                        whileTap: {\n                                                            scale: 0.9\n                                                        },\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiX, {\n                                                            className: \"w-5 h-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 109,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 103,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 flex items-center space-x-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 rounded-lg px-3 py-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-medium\",\n                                                            children: [\n                                                                students.length,\n                                                                \" \",\n                                                                students.length === 1 ? 'Student' : 'Students'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 116,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/20 rounded-lg px-3 py-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-white text-sm font-medium\",\n                                                            children: [\n                                                                students.filter((s)=>s.canEdit).length,\n                                                                \" Can Edit\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 121,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                                        children: (!isReady || !isConnected) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                height: 'auto'\n                                            },\n                                            exit: {\n                                                opacity: 0,\n                                                height: 0\n                                            },\n                                            className: \"bg-yellow-500/10 border-b border-yellow-500/20 px-6 py-3\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2 text-yellow-400 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Connecting to server...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 131,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-6 max-h-96 overflow-y-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                initial: {\n                                                    opacity: 0\n                                                },\n                                                animate: {\n                                                    opacity: 1\n                                                },\n                                                className: \"text-zinc-400 flex flex-col items-center py-12 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {\n                                                        className: \"w-16 h-16 mb-4 text-zinc-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg font-medium mb-2\",\n                                                        children: \"No students yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-zinc-500\",\n                                                        children: \"Students will appear here when they join the room\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 23\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: students.map((student, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                                        initial: {\n                                                            opacity: 0,\n                                                            y: 20\n                                                        },\n                                                        animate: {\n                                                            opacity: 1,\n                                                            y: 0\n                                                        },\n                                                        transition: {\n                                                            delay: index * 0.1\n                                                        },\n                                                        className: \"group bg-zinc-800 hover:bg-zinc-750 rounded-xl p-4 transition-all duration-200 border border-zinc-700 hover:border-zinc-600\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium\",\n                                                                            children: student.username.charAt(0).toUpperCase()\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 170,\n                                                                            columnNumber: 33\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-white\",\n                                                                                    children: student.username\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                    lineNumber: 174,\n                                                                                    columnNumber: 35\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center space-x-1 mt-1\",\n                                                                                    children: student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-flex items-center px-2 py-1 rounded-md bg-green-500/20 text-green-400 text-xs font-medium\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEdit3, {\n                                                                                                className: \"w-3 h-3 mr-1\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                                lineNumber: 178,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            \" Can Edit\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                        lineNumber: 177,\n                                                                                        columnNumber: 39\n                                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"inline-flex items-center px-2 py-1 rounded-md bg-zinc-600/50 text-zinc-300 text-xs font-medium\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEye, {\n                                                                                                className: \"w-3 h-3 mr-1\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                                lineNumber: 182,\n                                                                                                columnNumber: 41\n                                                                                            }, this),\n                                                                                            \" View Only\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                        lineNumber: 181,\n                                                                                        columnNumber: 39\n                                                                                    }, this)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                    lineNumber: 175,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 173,\n                                                                            columnNumber: 33\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                    lineNumber: 169,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                                    whileHover: {\n                                                                        scale: 1.05\n                                                                    },\n                                                                    whileTap: {\n                                                                        scale: 0.95\n                                                                    },\n                                                                    className: \"flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 \".concat(student.canEdit ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600 border border-zinc-600', \" \").concat(pending === student.socketId || !isReady || !isConnected ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                                                    onClick: ()=>handleToggle(student),\n                                                                    disabled: pending === student.socketId || !isReady || !isConnected,\n                                                                    title: student.canEdit ? 'Revoke edit access' : 'Grant edit access',\n                                                                    children: [\n                                                                        pending === student.socketId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 206,\n                                                                            columnNumber: 35\n                                                                        }, this) : student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUnlock, {\n                                                                            className: \"w-4 h-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 208,\n                                                                            columnNumber: 35\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_FiX_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLock, {\n                                                                            className: \"w-4 h-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                            lineNumber: 210,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        student.canEdit ? 'Revoke' : 'Grant'\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                    lineNumber: 189,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 168,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    }, student.socketId, false, {\n                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 27\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherControlPanel, \"XXvsg8u3UHfjO4bxcw/s4839Jw0=\", false, function() {\n    return [\n        _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission,\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService\n    ];\n});\n_c = TeacherControlPanel;\nvar _c;\n$RefreshReg$(_c, \"TeacherControlPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TeacherControlPanel.tsx\n"));

/***/ })

});