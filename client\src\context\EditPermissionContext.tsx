'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useSocketService } from '@/hooks/useSocketService';

interface Student {
  socketId: string;
  username: string;
  userId: string;
  email?: string;
  canEdit: boolean;
  joinedAt: string;
  lastActivity: string;
}

interface User {
  username: string;
  role: 'teacher' | 'student';
  socketId: string;
  userId: string;
  canEdit: boolean;
}

interface EditPermissionContextType {
  canEdit: boolean;
  isTeacher: boolean;
  users: User[];
  students: Student[];
  permissionBadge: 'teacher' | 'edit-access' | 'view-only';
  grantEditPermission: (targetSocketId: string) => void;
  revokeEditPermission: (targetSocketId: string) => void;
  setEditPermission: (targetSocketId: string, canEdit: boolean) => void;
  updateUserPermission: (socketId: string, canEdit: boolean) => void;
  setUsers: (users: User[]) => void;
  setStudents: (students: Student[]) => void;
  setCanEdit: (canEdit: boolean) => void;
  setIsTeacher: (isTeacher: boolean) => void;
}

const EditPermissionContext = createContext<EditPermissionContextType | undefined>(undefined);

interface EditPermissionProviderProps {
  children: ReactNode;
}

export function EditPermissionProvider({ children }: EditPermissionProviderProps) {
  const [canEdit, setCanEdit] = useState<boolean>(false);
  const [isTeacher, setIsTeacher] = useState<boolean>(false);
  const [users, setUsers] = useState<User[]>([]);
  const [students, setStudents] = useState<Student[]>([]);

  // Use the socket service hook
  const { socketService, isReady: socketReady, isConnected } = useSocketService();

  // Compute permission badge based on current state
  const permissionBadge: 'teacher' | 'edit-access' | 'view-only' =
    isTeacher ? 'teacher' : (canEdit ? 'edit-access' : 'view-only');

  // Grant edit permission to a student
  const grantEditPermission = (targetSocketId: string) => {
    if (!isTeacher) {
      console.warn('Only teachers can grant edit permissions');
      return;
    }

    if (!socketService || !socketReady) {
      console.warn('Socket service not ready, cannot grant edit permission');
      return;
    }

    // Get room ID from URL path
    const roomId = typeof window !== 'undefined' ?
      window.location.pathname.split('/').pop() : null;

    if (!roomId) {
      console.error('No room ID found in URL path');
      return;
    }

    console.log(`Granting edit permission to ${targetSocketId} in room ${roomId}`);

    try {
      // Use the socket directly for custom events
      const sock = socketService.getSocket && socketService.getSocket();
      if (sock && typeof sock.emit === 'function') {
        (sock.emit as any)('grant-edit-permission', { roomId, targetSocketId });
      } else {
        console.error('Socket instance not available for grant-edit-permission');
      }
    } catch (error) {
      console.error('Error granting edit permission:', error);
    }
  };

  // Revoke edit permission from a student
  const revokeEditPermission = (targetSocketId: string) => {
    if (!isTeacher) {
      console.warn('Only teachers can revoke edit permissions');
      return;
    }

    if (!socketService || !socketReady) {
      console.warn('Socket service not ready, cannot revoke edit permission');
      return;
    }

    // Get room ID from URL path
    const roomId = typeof window !== 'undefined' ?
      window.location.pathname.split('/').pop() : null;

    if (!roomId) {
      console.error('No room ID found in URL path');
      return;
    }

    console.log(`Revoking edit permission from ${targetSocketId} in room ${roomId}`);

    try {
      // Use the socket directly for custom events
      const sock = socketService.getSocket && socketService.getSocket();
      if (sock && typeof sock.emit === 'function') {
        (sock.emit as any)('revoke-edit-permission', { roomId, targetSocketId });
      } else {
        console.error('Socket instance not available for revoke-edit-permission');
      }
    } catch (error) {
      console.error('Error revoking edit permission:', error);
    }
  };

  // Legacy method for backward compatibility
  const setEditPermission = (targetSocketId: string, canEdit: boolean) => {
    if (canEdit) {
      grantEditPermission(targetSocketId);
    } else {
      revokeEditPermission(targetSocketId);
    }
  };

  const updateUserPermission = (socketId: string, canEdit: boolean) => {
    setUsers(prevUsers => 
      prevUsers.map(user => 
        user.socketId === socketId 
          ? { ...user, canEdit }
          : user
      )
    );
  };

  useEffect(() => {
    // Only set up listeners when socket is ready
    if (!socketReady || !socketService) {
      console.log('Waiting for socket to be ready for edit permissions...');
      return;
    }

    // Listen for edit permission changes
    const handleEditPermission = (data: { canEdit: boolean }) => {
      console.log('Received edit permission update:', data);
      setCanEdit(data.canEdit);
    };

    // Listen for permission updates (new event)
    const handlePermissionUpdated = (data: { canEdit: boolean }) => {
      console.log('Received permission-updated event:', data);
      setCanEdit(data.canEdit);
    };

    // Listen for student list updates (for teachers) - ROBUST VERSION
    const handleUpdateStudentList = (data: any) => {
      console.log('🎯 [ROBUST] Received update-student-list event:', JSON.stringify(data, null, 2));

      // Robust validation
      if (!data) {
        console.warn('🚨 [ROBUST] update-student-list event received with null/undefined data');
        return;
      }

      if (!data.students) {
        console.warn('🚨 [ROBUST] update-student-list event missing students property:', data);
        return;
      }

      if (!Array.isArray(data.students)) {
        console.warn('🚨 [ROBUST] update-student-list students is not an array:', typeof data.students, data.students);
        return;
      }

      console.log(`✅ [ROBUST] Valid students array received with ${data.students.length} items`);

      // The backend already filters to only students, so we trust the data
      // Just ensure each student has required properties
      const validStudents = data.students.filter((student: any) => {
        const isValid = student &&
                       typeof student === 'object' &&
                       student.socketId &&
                       student.username &&
                       student.userId;

        if (!isValid) {
          console.warn('🚨 [ROBUST] Invalid student object:', student);
        }

        return isValid;
      });

      console.log(`✅ [ROBUST] Setting ${validStudents.length} valid students to state`);
      setStudents(validStudents);
    };

    // 🔥 FIXED: Unified handler for room users updates
    const handleRoomUsersUpdated = (data: { users: User[]; count: number }) => {
      console.log('🔄 [TEACHER_SYNC] Room users updated with permissions:', data);

      if (!data || !Array.isArray(data.users)) {
        console.warn('⚠️ [TEACHER_SYNC] Invalid room users data received:', data);
        return;
      }

      // Update the users state
      setUsers(data.users);

      // 🔥 KEY FIX: Also update students state for teachers in real-time
      if (isTeacher) {
        const studentsFromUsers = data.users
          .filter(user => user.role === 'student')
          .map(user => ({
            socketId: user.socketId,
            username: user.username,
            userId: user.userId,
            canEdit: user.canEdit || false,
            joinedAt: new Date().toISOString(),
            lastActivity: new Date().toISOString()
          }));

        console.log(`🔄 [TEACHER_SYNC] Updating students from room-users-updated: ${studentsFromUsers.length} students`);
        setStudents(studentsFromUsers);
      }
    };

    // 🔥 OPTIMIZED: Simplified user-joined handler (room-users-updated handles the state)
    const handleUserJoined = (data: any) => {
      console.log('🔄 [TEACHER_SYNC] User joined event received:', data);
      // The room-users-updated event will handle the actual state update automatically
      // This is just for logging and potential future enhancements
    };

    // Set up socket listeners with null checks
    try {
      socketService.on('edit-permission', handleEditPermission);
      socketService.on('permission-updated', handlePermissionUpdated);
      socketService.on('update-student-list', handleUpdateStudentList);
      socketService.on('room-users-updated', handleRoomUsersUpdated);
      socketService.on('user-joined', handleUserJoined);
      console.log('Enhanced permission socket listeners set up successfully');
    } catch (error) {
      console.error('Error setting up socket listeners:', error);
    }

    // Cleanup listeners
    return () => {
      try {
        if (socketService) {
          socketService.off('edit-permission', handleEditPermission);
          socketService.off('permission-updated', handlePermissionUpdated);
          socketService.off('update-student-list', handleUpdateStudentList);
          socketService.off('room-users-updated', handleRoomUsersUpdated);
          socketService.off('user-joined', handleUserJoined);
          console.log('Enhanced permission socket listeners cleaned up');
        }
      } catch (error) {
        console.error('Error cleaning up socket listeners:', error);
      }
    };
  }, [socketReady, isTeacher]); // 🔥 IMPORTANT: Added isTeacher dependency

  // In the effect that requests the student list, use the correct public method
  useEffect(() => {
    if (!socketReady || !socketService || !isTeacher) {
      return;
    }

    // If this is a teacher, request the initial student list
    const currentRoomId = window.location.pathname.split('/').pop();
    if (currentRoomId) {
      console.log('Teacher role detected, requesting initial student list for room:', currentRoomId);
      // FIX: Use the underlying socket directly for custom events
      const sock = socketService.getSocket && socketService.getSocket();
      if (sock && typeof sock.emit === 'function') {
        sock.emit('request-student-list', { roomId: currentRoomId });
      } else {
        console.warn('Socket instance not available for request-student-list emit');
      }
    }
  }, [socketReady, isTeacher, socketService]);

  // Log permission changes for debugging
  useEffect(() => {
    console.log(`Edit permission state: canEdit=${canEdit}, isTeacher=${isTeacher}, socketReady=${socketReady}, isConnected=${isConnected}`);
  }, [canEdit, isTeacher, socketReady, isConnected]);

  const value: EditPermissionContextType = {
    canEdit,
    isTeacher,
    users,
    students,
    permissionBadge,
    grantEditPermission,
    revokeEditPermission,
    setEditPermission,
    updateUserPermission,
    setUsers,
    setStudents,
    setCanEdit,
    setIsTeacher
  };

  return (
    <EditPermissionContext.Provider value={value}>
      {children}
    </EditPermissionContext.Provider>
  );
}

export function useEditPermission() {
  const context = useContext(EditPermissionContext);
  if (context === undefined) {
    throw new Error('useEditPermission must be used within an EditPermissionProvider');
  }
  return context;
}
