"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/context/EditPermissionContext.tsx":
/*!***********************************************!*\
  !*** ./src/context/EditPermissionContext.tsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditPermissionProvider: () => (/* binding */ EditPermissionProvider),\n/* harmony export */   useEditPermission: () => (/* binding */ useEditPermission)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ EditPermissionProvider,useEditPermission auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst EditPermissionContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction EditPermissionProvider(param) {\n    let { children } = param;\n    _s();\n    const [canEdit, setCanEdit] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isTeacher, setIsTeacher] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Use the socket service hook\n    const { socketService, isReady: socketReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService)();\n    // Compute permission badge based on current state\n    const permissionBadge = isTeacher ? 'teacher' : canEdit ? 'edit-access' : 'view-only';\n    // Grant edit permission to a student\n    const grantEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can grant edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot grant edit permission');\n            return;\n        }\n        // Get room ID from URL path\n        const roomId =  true ? window.location.pathname.split('/').pop() : 0;\n        if (!roomId) {\n            console.error('No room ID found in URL path');\n            return;\n        }\n        console.log(\"Granting edit permission to \".concat(targetSocketId, \" in room \").concat(roomId));\n        try {\n            // Use the socket directly for custom events\n            const sock = socketService.getSocket && socketService.getSocket();\n            if (sock && typeof sock.emit === 'function') {\n                sock.emit('grant-edit-permission', {\n                    roomId,\n                    targetSocketId\n                });\n            } else {\n                console.error('Socket instance not available for grant-edit-permission');\n            }\n        } catch (error) {\n            console.error('Error granting edit permission:', error);\n        }\n    };\n    // Revoke edit permission from a student\n    const revokeEditPermission = (targetSocketId)=>{\n        if (!isTeacher) {\n            console.warn('Only teachers can revoke edit permissions');\n            return;\n        }\n        if (!socketService || !socketReady) {\n            console.warn('Socket service not ready, cannot revoke edit permission');\n            return;\n        }\n        // Get room ID from URL path\n        const roomId =  true ? window.location.pathname.split('/').pop() : 0;\n        if (!roomId) {\n            console.error('No room ID found in URL path');\n            return;\n        }\n        console.log(\"Revoking edit permission from \".concat(targetSocketId, \" in room \").concat(roomId));\n        try {\n            // Use the socket directly for custom events\n            const sock = socketService.getSocket && socketService.getSocket();\n            if (sock && typeof sock.emit === 'function') {\n                sock.emit('revoke-edit-permission', {\n                    roomId,\n                    targetSocketId\n                });\n            } else {\n                console.error('Socket instance not available for revoke-edit-permission');\n            }\n        } catch (error) {\n            console.error('Error revoking edit permission:', error);\n        }\n    };\n    // Legacy method for backward compatibility\n    const setEditPermission = (targetSocketId, canEdit)=>{\n        if (canEdit) {\n            grantEditPermission(targetSocketId);\n        } else {\n            revokeEditPermission(targetSocketId);\n        }\n    };\n    const updateUserPermission = (socketId, canEdit)=>{\n        setUsers((prevUsers)=>prevUsers.map((user)=>user.socketId === socketId ? {\n                    ...user,\n                    canEdit\n                } : user));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            // Only set up listeners when socket is ready\n            if (!socketReady || !socketService) {\n                console.log('Waiting for socket to be ready for edit permissions...');\n                return;\n            }\n            // Listen for edit permission changes\n            const handleEditPermission = {\n                \"EditPermissionProvider.useEffect.handleEditPermission\": (data)=>{\n                    console.log('Received edit permission update:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handleEditPermission\"];\n            // Listen for permission updates (new event)\n            const handlePermissionUpdated = {\n                \"EditPermissionProvider.useEffect.handlePermissionUpdated\": (data)=>{\n                    console.log('Received permission-updated event:', data);\n                    setCanEdit(data.canEdit);\n                }\n            }[\"EditPermissionProvider.useEffect.handlePermissionUpdated\"];\n            // Listen for student list updates (for teachers) - ROBUST VERSION\n            const handleUpdateStudentList = {\n                \"EditPermissionProvider.useEffect.handleUpdateStudentList\": (data)=>{\n                    console.log('🎯 [ROBUST] Received update-student-list event:', JSON.stringify(data, null, 2));\n                    // Robust validation\n                    if (!data) {\n                        console.warn('🚨 [ROBUST] update-student-list event received with null/undefined data');\n                        return;\n                    }\n                    if (!data.students) {\n                        console.warn('🚨 [ROBUST] update-student-list event missing students property:', data);\n                        return;\n                    }\n                    if (!Array.isArray(data.students)) {\n                        console.warn('🚨 [ROBUST] update-student-list students is not an array:', typeof data.students, data.students);\n                        return;\n                    }\n                    console.log(\"✅ [ROBUST] Valid students array received with \".concat(data.students.length, \" items\"));\n                    // The backend already filters to only students, so we trust the data\n                    // Just ensure each student has required properties\n                    const validStudents = data.students.filter({\n                        \"EditPermissionProvider.useEffect.handleUpdateStudentList.validStudents\": (student)=>{\n                            const isValid = student && typeof student === 'object' && student.socketId && student.username && student.userId;\n                            if (!isValid) {\n                                console.warn('🚨 [ROBUST] Invalid student object:', student);\n                            }\n                            return isValid;\n                        }\n                    }[\"EditPermissionProvider.useEffect.handleUpdateStudentList.validStudents\"]);\n                    console.log(\"✅ [ROBUST] Setting \".concat(validStudents.length, \" valid students to state\"));\n                    setStudents(validStudents);\n                }\n            }[\"EditPermissionProvider.useEffect.handleUpdateStudentList\"];\n            // 🔥 FIXED: Unified handler for room users updates\n            const handleRoomUsersUpdated = {\n                \"EditPermissionProvider.useEffect.handleRoomUsersUpdated\": (data)=>{\n                    console.log('🔄 [TEACHER_SYNC] Room users updated with permissions:', data);\n                    if (!data || !Array.isArray(data.users)) {\n                        console.warn('⚠️ [TEACHER_SYNC] Invalid room users data received:', data);\n                        return;\n                    }\n                    // Update the users state\n                    setUsers(data.users);\n                    // 🔥 KEY FIX: Also update students state for teachers in real-time\n                    if (isTeacher) {\n                        const studentsFromUsers = data.users.filter({\n                            \"EditPermissionProvider.useEffect.handleRoomUsersUpdated.studentsFromUsers\": (user)=>user.role === 'student'\n                        }[\"EditPermissionProvider.useEffect.handleRoomUsersUpdated.studentsFromUsers\"]).map({\n                            \"EditPermissionProvider.useEffect.handleRoomUsersUpdated.studentsFromUsers\": (user)=>({\n                                    socketId: user.socketId,\n                                    username: user.username,\n                                    userId: user.userId,\n                                    canEdit: user.canEdit || false,\n                                    joinedAt: new Date().toISOString(),\n                                    lastActivity: new Date().toISOString()\n                                })\n                        }[\"EditPermissionProvider.useEffect.handleRoomUsersUpdated.studentsFromUsers\"]);\n                        console.log(\"\\uD83D\\uDD04 [TEACHER_SYNC] Updating students from room-users-updated: \".concat(studentsFromUsers.length, \" students\"));\n                        setStudents(studentsFromUsers);\n                    }\n                }\n            }[\"EditPermissionProvider.useEffect.handleRoomUsersUpdated\"];\n            // 🔥 OPTIMIZED: Simplified user-joined handler (room-users-updated handles the state)\n            const handleUserJoined = {\n                \"EditPermissionProvider.useEffect.handleUserJoined\": (data)=>{\n                    console.log('🔄 [TEACHER_SYNC] User joined event received:', data);\n                // The room-users-updated event will handle the actual state update automatically\n                // This is just for logging and potential future enhancements\n                }\n            }[\"EditPermissionProvider.useEffect.handleUserJoined\"];\n            // Set up socket listeners with null checks\n            try {\n                socketService.on('edit-permission', handleEditPermission);\n                socketService.on('permission-updated', handlePermissionUpdated);\n                socketService.on('update-student-list', handleUpdateStudentList);\n                socketService.on('room-users-updated', handleRoomUsersUpdated);\n                socketService.on('user-joined', handleUserJoined);\n                console.log('Enhanced permission socket listeners set up successfully');\n            } catch (error) {\n                console.error('Error setting up socket listeners:', error);\n            }\n            // Cleanup listeners\n            return ({\n                \"EditPermissionProvider.useEffect\": ()=>{\n                    try {\n                        if (socketService) {\n                            socketService.off('edit-permission', handleEditPermission);\n                            socketService.off('permission-updated', handlePermissionUpdated);\n                            socketService.off('update-student-list', handleUpdateStudentList);\n                            socketService.off('room-users-updated', handleRoomUsersUpdated);\n                            socketService.off('user-joined', handleUserJoined);\n                            console.log('Enhanced permission socket listeners cleaned up');\n                        }\n                    } catch (error) {\n                        console.error('Error cleaning up socket listeners:', error);\n                    }\n                }\n            })[\"EditPermissionProvider.useEffect\"];\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        socketReady,\n        isTeacher\n    ]); // 🔥 IMPORTANT: Added isTeacher dependency\n    // In the effect that requests the student list, use the correct public method\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            if (!socketReady || !socketService || !isTeacher) {\n                return;\n            }\n            // If this is a teacher, request the initial student list\n            const currentRoomId = window.location.pathname.split('/').pop();\n            if (currentRoomId) {\n                console.log('Teacher role detected, requesting initial student list for room:', currentRoomId);\n                // FIX: Use the underlying socket directly for custom events\n                const sock = socketService.getSocket && socketService.getSocket();\n                if (sock && typeof sock.emit === 'function') {\n                    sock.emit('request-student-list', {\n                        roomId: currentRoomId\n                    });\n                } else {\n                    console.warn('Socket instance not available for request-student-list emit');\n                }\n            }\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        socketReady,\n        isTeacher,\n        socketService\n    ]);\n    // Log permission changes for debugging\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPermissionProvider.useEffect\": ()=>{\n            console.log(\"Edit permission state: canEdit=\".concat(canEdit, \", isTeacher=\").concat(isTeacher, \", socketReady=\").concat(socketReady, \", isConnected=\").concat(isConnected));\n        }\n    }[\"EditPermissionProvider.useEffect\"], [\n        canEdit,\n        isTeacher,\n        socketReady,\n        isConnected\n    ]);\n    const value = {\n        canEdit,\n        isTeacher,\n        users,\n        students,\n        permissionBadge,\n        grantEditPermission,\n        revokeEditPermission,\n        setEditPermission,\n        updateUserPermission,\n        setUsers,\n        setStudents,\n        setCanEdit,\n        setIsTeacher\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditPermissionContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\context\\\\EditPermissionContext.tsx\",\n        lineNumber: 319,\n        columnNumber: 5\n    }, this);\n}\n_s(EditPermissionProvider, \"udh6tS1itqpINKcvqOf60EZ6C1A=\", false, function() {\n    return [\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_2__.useSocketService\n    ];\n});\n_c = EditPermissionProvider;\nfunction useEditPermission() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(EditPermissionContext);\n    if (context === undefined) {\n        throw new Error('useEditPermission must be used within an EditPermissionProvider');\n    }\n    return context;\n}\n_s1(useEditPermission, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"EditPermissionProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/context/EditPermissionContext.tsx\n"));

/***/ })

});