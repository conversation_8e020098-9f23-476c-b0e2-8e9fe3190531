"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/TeacherControlPanel.tsx":
/*!************************************************!*\
  !*** ./src/components/TeacherControlPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherControlPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiChevronRight_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=FiChevronRight,FiEdit3,FiEye,FiLock,FiUnlock,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction TeacherControlPanel(param) {\n    let { className = '' } = param;\n    _s();\n    const { isTeacher, students, grantEditPermission, revokeEditPermission } = (0,_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission)();\n    const { isReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService)();\n    const [pending, setPending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Only show panel for teachers\n    if (!isTeacher) {\n        return null;\n    }\n    const handleToggle = (student)=>{\n        setPending(student.socketId);\n        if (student.canEdit) {\n            revokeEditPermission(student.socketId);\n        } else {\n            grantEditPermission(student.socketId);\n        }\n        setTimeout(()=>setPending(null), 1000);\n    };\n    const toggleCollapse = ()=>{\n        setIsCollapsed(!isCollapsed);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            x: 0\n        },\n        animate: {\n            x: isCollapsed ? 280 : 0\n        },\n        transition: {\n            duration: 0.3,\n            ease: \"easeInOut\"\n        },\n        className: \"fixed top-20 right-4 z-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                onClick: toggleCollapse,\n                className: \"absolute -left-10 top-4 bg-zinc-800 hover:bg-zinc-700 text-white p-2 rounded-l-lg shadow-lg transition-all duration-200 border border-zinc-600\",\n                whileHover: {\n                    scale: 1.05\n                },\n                whileTap: {\n                    scale: 0.95\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    animate: {\n                        rotate: isCollapsed ? 180 : 0\n                    },\n                    transition: {\n                        duration: 0.3\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiChevronRight_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiChevronRight, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                    lineNumber: 52,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.aside, {\n                className: \"w-80 bg-zinc-900 border border-zinc-700 rounded-2xl shadow-2xl overflow-hidden\",\n                initial: {\n                    opacity: 0,\n                    scale: 0.95\n                },\n                animate: {\n                    opacity: 1,\n                    scale: 1\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-blue-600 to-purple-600 p-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiChevronRight_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {\n                                            className: \"text-white w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-lg font-bold text-white\",\n                                            children: \"Student Access\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-white/20 text-white text-xs px-2 py-1 rounded-full\",\n                                        children: [\n                                            students.length,\n                                            \" \",\n                                            students.length === 1 ? 'student' : 'students'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                    lineNumber: 74,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                        children: (!isReady || !isConnected) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                            initial: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            animate: {\n                                opacity: 1,\n                                height: 'auto'\n                            },\n                            exit: {\n                                opacity: 0,\n                                height: 0\n                            },\n                            className: \"bg-yellow-500/10 border-b border-yellow-500/20 px-4 py-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 text-yellow-400 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-yellow-400 rounded-full animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Connecting...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 93,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 max-h-96 overflow-y-auto\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                            children: students.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                className: \"text-zinc-400 flex flex-col items-center py-8 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiChevronRight_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUsers, {\n                                        className: \"w-12 h-12 mb-3 text-zinc-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm\",\n                                        children: \"No students in the room yet\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xs text-zinc-500 mt-1\",\n                                        children: \"Students will appear here when they join\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 110,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3\",\n                                children: students.map((student, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            delay: index * 0.1\n                                        },\n                                        className: \"group bg-zinc-800 hover:bg-zinc-750 rounded-xl p-3 transition-all duration-200 border border-zinc-700 hover:border-zinc-600\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-medium\",\n                                                            children: student.username.charAt(0).toUpperCase()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 124,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium text-white text-sm\",\n                                                                    children: student.username\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-1 mt-1\",\n                                                                    children: student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2 py-0.5 rounded-md bg-green-500/20 text-green-400 text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiChevronRight_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEdit3, {\n                                                                                className: \"w-3 h-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                lineNumber: 132,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            \" Can Edit\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                        lineNumber: 131,\n                                                                        columnNumber: 31\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2 py-0.5 rounded-md bg-zinc-600/50 text-zinc-300 text-xs\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiChevronRight_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiEye, {\n                                                                                className: \"w-3 h-3 mr-1\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                                lineNumber: 136,\n                                                                                columnNumber: 33\n                                                                            }, this),\n                                                                            \" View Only\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                        lineNumber: 135,\n                                                                        columnNumber: 31\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                                    lineNumber: 129,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 127,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.button, {\n                                                    whileHover: {\n                                                        scale: 1.05\n                                                    },\n                                                    whileTap: {\n                                                        scale: 0.95\n                                                    },\n                                                    className: \"flex items-center px-3 py-2 rounded-lg text-xs font-medium transition-all duration-200 \".concat(student.canEdit ? 'bg-green-500/20 text-green-400 hover:bg-green-500/30 border border-green-500/30' : 'bg-zinc-700 text-zinc-300 hover:bg-zinc-600 border border-zinc-600', \" \").concat(pending === student.socketId || !isReady || !isConnected ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                                    onClick: ()=>handleToggle(student),\n                                                    disabled: pending === student.socketId || !isReady || !isConnected,\n                                                    title: student.canEdit ? 'Revoke edit access' : 'Grant edit access',\n                                                    children: [\n                                                        pending === student.socketId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 border border-current border-t-transparent rounded-full animate-spin mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 160,\n                                                            columnNumber: 27\n                                                        }, this) : student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiChevronRight_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiUnlock, {\n                                                            className: \"w-3 h-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 27\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiChevronRight_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_5__.FiLock, {\n                                                            className: \"w-3 h-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                            lineNumber: 164,\n                                                            columnNumber: 27\n                                                        }, this),\n                                                        student.canEdit ? 'Revoke' : 'Grant'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, student.socketId, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n        lineNumber: 39,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherControlPanel, \"CAoLoc7MZ+D9cxs0U3PrgX8X4Dk=\", false, function() {\n    return [\n        _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission,\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService\n    ];\n});\n_c = TeacherControlPanel;\nvar _c;\n$RefreshReg$(_c, \"TeacherControlPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TeacherControlPanel.tsx\n"));

/***/ })

});