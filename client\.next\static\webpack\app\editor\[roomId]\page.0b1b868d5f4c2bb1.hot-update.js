"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/app/editor/[roomId]/page.tsx":
/*!******************************************!*\
  !*** ./src/app/editor/[roomId]/page.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_CodeEditor__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/CodeEditor */ \"(app-pages-browser)/./src/components/CodeEditor.tsx\");\n/* harmony import */ var _components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ProtectedRoute */ \"(app-pages-browser)/./src/components/ProtectedRoute.tsx\");\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _components_TeacherControlPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/TeacherControlPanel */ \"(app-pages-browser)/./src/components/TeacherControlPanel.tsx\");\n/* harmony import */ var _components_PermissionBadge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/PermissionBadge */ \"(app-pages-browser)/./src/components/PermissionBadge.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction EditorPage() {\n    _s();\n    // Get the params and handle the type safely\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    // Extract roomId and ensure it's a string\n    const roomId = params === null || params === void 0 ? void 0 : params.roomId;\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"Anonymous\") // Default username\n    ;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"EditorPage.useEffect\": ()=>{\n            // Debugging: Check if roomId is being extracted\n            console.log(\"Extracted roomId:\", roomId);\n            if (!roomId) {\n                console.error(\"Invalid or missing room ID\");\n                return;\n            }\n            if (true) {\n                const storedUsername = localStorage.getItem(\"username\");\n                if (storedUsername) {\n                    console.log(\"Using stored username:\", storedUsername);\n                    setUsername(storedUsername);\n                } else {\n                    console.log(\"No stored username found, redirecting to dashboard\");\n                    // Generate a random username suffix\n                    const randomSuffix = Math.floor(Math.random() * 1000).toString().padStart(3, '0');\n                    const defaultUsername = \"User\".concat(randomSuffix);\n                    localStorage.setItem(\"username\", defaultUsername);\n                    setUsername(defaultUsername);\n                }\n            }\n        }\n    }[\"EditorPage.useEffect\"], [\n        roomId\n    ]);\n    if (!roomId) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"Error: Room ID is missing. Please join a valid room.\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n            lineNumber: 45,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProtectedRoute__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_5__.EditPermissionProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex flex-col items-center justify-center min-h-screen p-4 gap-6 relative overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 -z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-b from-blue-900/10 to-purple-900/10 dark:from-blue-900/20 dark:to-purple-900/20\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-grid-pattern opacity-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-7xl flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-500 to-purple-600 bg-clip-text text-transparent\",\n                                children: [\n                                    \"RealCode - Room: \",\n                                    roomId\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PermissionBadge__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                size: \"lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full max-w-7xl flex gap-6 h-[calc(100vh-200px)]\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CodeEditor__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        roomId: roomId,\n                                        username: username\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_PermissionBadge__WEBPACK_IMPORTED_MODULE_7__.EditorPermissionStatus, {}, void 0, false, {\n                                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 flex-shrink-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TeacherControlPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n                lineNumber: 51,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\app\\\\editor\\\\[roomId]\\\\page.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n_s(EditorPage, \"z1zVpeEpIf1lxrjmMEpnU5z1etI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_1__.useParams\n    ];\n});\n_c = EditorPage;\nvar _c;\n$RefreshReg$(_c, \"EditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/editor/[roomId]/page.tsx\n"));

/***/ })

});