"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/editor/[roomId]/page",{

/***/ "(app-pages-browser)/./src/components/TeacherControlPanel.tsx":
/*!************************************************!*\
  !*** ./src/components/TeacherControlPanel.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TeacherControlPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=FiEdit3,FiEye,FiLock,FiUnlock,FiUsers!=!react-icons/fi */ \"(app-pages-browser)/./node_modules/react-icons/fi/index.mjs\");\n/* harmony import */ var _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/context/EditPermissionContext */ \"(app-pages-browser)/./src/context/EditPermissionContext.tsx\");\n/* harmony import */ var _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSocketService */ \"(app-pages-browser)/./src/hooks/useSocketService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction TeacherControlPanel(param) {\n    let { className = '' } = param;\n    _s();\n    const { isTeacher, students, grantEditPermission, revokeEditPermission } = (0,_context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission)();\n    const { isReady, isConnected } = (0,_hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService)();\n    const [pending, setPending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isCollapsed, setIsCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Debug logging for student list updates\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"TeacherControlPanel.useEffect\": ()=>{\n            console.log('🎯 [TEACHER_PANEL] State update:', {\n                isTeacher,\n                studentCount: students.length,\n                isReady,\n                isConnected,\n                students: students.map({\n                    \"TeacherControlPanel.useEffect\": (s)=>({\n                            username: s.username,\n                            socketId: s.socketId,\n                            canEdit: s.canEdit\n                        })\n                }[\"TeacherControlPanel.useEffect\"])\n            });\n        }\n    }[\"TeacherControlPanel.useEffect\"], [\n        students,\n        isTeacher,\n        isReady,\n        isConnected\n    ]);\n    // Only show panel for teachers\n    if (!isTeacher) {\n        console.log('🎯 [TEACHER_PANEL] Not showing panel (not teacher)');\n        return null;\n    }\n    const handleToggle = (student)=>{\n        setPending(student.socketId);\n        if (student.canEdit) {\n            revokeEditPermission(student.socketId);\n        } else {\n            grantEditPermission(student.socketId);\n        }\n        setTimeout(()=>setPending(null), 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: \"w-full max-w-md bg-white border rounded-lg shadow-lg p-4 \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUsers, {\n                        className: \"text-blue-500 mr-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-bold\",\n                        children: \"Student Access Panel\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            !isReady || !isConnected ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-yellow-600 text-sm mb-2\",\n                children: \"Waiting for connection...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 57,\n                columnNumber: 9\n            }, this) : null,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    students.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 flex flex-col items-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUsers, {\n                                className: \"w-8 h-8 mb-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"No students in the room yet\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 11\n                    }, this),\n                    students.map((student)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between bg-gray-50 rounded px-3 py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-medium\",\n                                            children: student.username\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 15\n                                        }, this),\n                                        student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded bg-green-100 text-green-700 text-xs ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEdit3, {\n                                                    className: \"w-3 h-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                    lineNumber: 72,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" Edit\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex items-center px-2 py-0.5 rounded bg-gray-100 text-gray-600 text-xs ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiEye, {\n                                                    className: \"w-3 h-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                    lineNumber: 76,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \" View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                            lineNumber: 75,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                    lineNumber: 68,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: \"flex items-center px-3 py-1 rounded text-xs font-medium transition-all \".concat(student.canEdit ? 'bg-green-100 text-green-700 hover:bg-green-200' : 'bg-gray-100 text-gray-600 hover:bg-gray-200', \" \").concat(pending === student.socketId || !isReady || !isConnected ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'),\n                                    onClick: ()=>handleToggle(student),\n                                    disabled: pending === student.socketId || !isReady || !isConnected,\n                                    title: student.canEdit ? 'Revoke edit access' : 'Grant edit access',\n                                    children: student.canEdit ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiUnlock, {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 36\n                                            }, this),\n                                            \" Revoke\"\n                                        ]\n                                    }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FiEdit3_FiEye_FiLock_FiUnlock_FiUsers_react_icons_fi__WEBPACK_IMPORTED_MODULE_4__.FiLock, {\n                                                className: \"w-3 h-3 mr-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 88\n                                            }, this),\n                                            \" Grant\"\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, student.socketId, true, {\n                            fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 11\n                        }, this))\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Project\\\\realcode\\\\client\\\\src\\\\components\\\\TeacherControlPanel.tsx\",\n        lineNumber: 51,\n        columnNumber: 5\n    }, this);\n}\n_s(TeacherControlPanel, \"j4v7cwSrX7g5gTp3IF7Ks1XKjZs=\", false, function() {\n    return [\n        _context_EditPermissionContext__WEBPACK_IMPORTED_MODULE_2__.useEditPermission,\n        _hooks_useSocketService__WEBPACK_IMPORTED_MODULE_3__.useSocketService\n    ];\n});\n_c = TeacherControlPanel;\nvar _c;\n$RefreshReg$(_c, \"TeacherControlPanel\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TeacherControlPanel.tsx\n"));

/***/ })

});